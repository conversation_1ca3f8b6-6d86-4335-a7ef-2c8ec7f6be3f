# Lucide to Phosphor Icons Mapping

This document maps all Lucide React icons currently used in the codebase to their Phosphor Icons equivalents.

## Icon Mappings

| Lucide Icon | Phosphor Icon | Notes |
|-------------|---------------|-------|
| AlertTriangleIcon | Warning | |
| ArrowLeftIcon | ArrowLeft | |
| ArrowRightIcon | ArrowRight | |
| BadgeCheckIcon | CheckCircle | |
| BotMessageSquareIcon | ChatCircle | |
| Building2Icon | Buildings | |
| CheckCircle2Icon | CheckCircle | |
| CheckCircleIcon | CheckCircle | |
| CheckIcon | Check | |
| ChevronDownIcon | CaretDown | |
| ChevronLeftIcon | CaretLeft | |
| ChevronRightIcon | CaretRight | |
| ChevronsUpDownIcon | CaretUpDown | |
| ClockIcon | Clock | |
| CloudIcon | Cloud | |
| ComputerIcon | Desktop | |
| CookieIcon | Cookie | |
| CreditCardIcon | CreditCard | |
| EditIcon | PencilSimple | |
| EllipsisIcon | DotsThree | |
| EyeIcon | Eye | |
| EyeOffIcon | EyeSlash | |
| HardDriveIcon | HardDrive | |
| Home | House | |
| HomeIcon | House | |
| KeyIcon | Key | |
| LanguagesIcon | Translate | |
| LinkIcon | Link | |
| Loader2Icon | CircleNotch | Animated spinner |
| LogOutIcon | SignOut | |
| MailboxIcon | Mailbox | |
| MailCheckIcon | EnvelopeSimple | |
| MailIcon | Envelope | |
| MenuIcon | List | |
| MinusIcon | Minus | |
| MoonIcon | Moon | |
| MoreVerticalIcon | DotsThreeVertical | |
| PaperclipIcon | Paperclip | |
| PhoneIcon | Phone | |
| PlusCircleIcon | PlusCircle | |
| PlusIcon | Plus | |
| SendIcon | PaperPlaneTilt | |
| SettingsIcon | Gear | |
| SquareUserRoundIcon | UserCircle | |
| StarIcon | Star | |
| SunIcon | Sun | |
| TabletSmartphoneIcon | DeviceMobile | |
| TrashIcon | Trash | |
| UndoIcon | ArrowCounterClockwise | |
| UserCog2Icon | UserGear | |
| UserCogIcon | UserGear | |
| UsersIcon | Users | |
| WandIcon | MagicWand | |
| XIcon | X | |

## Import Statement Changes

### Before (Lucide):
```typescript
import { HomeIcon, SettingsIcon, UserCogIcon } from "lucide-react";
```

### After (Phosphor):
```typescript
import { House, Gear, UserGear } from "@phosphor-icons/react";
```

## Usage Notes

1. Phosphor icons don't use the "Icon" suffix
2. Some icon names are different (e.g., HomeIcon → House, SettingsIcon → Gear)
3. Phosphor icons support weight variants (thin, light, regular, bold, fill, duotone)
4. Default weight is "regular" if not specified
5. For animated icons like Loader2Icon, use CircleNotch with CSS animation

## Weight Usage Examples

```typescript
// Regular weight (default)
<House />

// Bold weight
<House weight="bold" />

// Fill weight
<House weight="fill" />
```
